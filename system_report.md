
# 双层多智能体仇恨言论检测框架技术报告

## 1. 系统概述

本项目实现了一个先进的**双层多智能体（Two-Layer Multi-Agent）**仇恨言论检测框架。该框架旨在结合快速、高效的初步筛选和精准、深入的大语言模型（LLM）分析，以应对日益复杂的网络仇恨言论，特别是难以识别的**隐式（Implicit）**和**对抗性（Adversarial）**言论。

### 1.1. 核心设计思想

系统设计的核心是**分层处理**和**智能调度**：

- **分层处理**：将检测任务分解为两个层次。第一层使用轻量级、高速度的智能体进行快速筛选，过滤掉大量明确的非仇恨言论和部分明显的仇恨言论。第二层则专注于处理第一层无法确定的“模糊”或“疑难”样本，使用计算密集但更精确的大语言模型进行深度分析。
- **智能调度**：在两层之间设计了一个精密的**增强歧义检测器（Enhanced Ambiguity Detector）**。它作为智能调度员，根据第一层多个智能体的输出和文本自身的深层语言特征，动态判断一个文本是否需要提交给第二层进行分析，从而在检测精度和处理效率之间取得平衡。

### 1.2. 系统架构

系统整体架构如下图所示：

```mermaid
graph TD
    subgraph Input
        A[输入文本]
    end

    subgraph Layer 1: 快速筛选层
        A --> B[ToxiGen RoBERTa Agent]
        A --> C[HurtLex Agent]
        B --> E{决策融合与歧义检测}
        C --> E
    end

    subgraph Layer 2: 精细分析层
        F[Single LLM Agent]
        G[Retrieval-Augmented LLM Agent]
        F --> H{最终决策融合}
        G --> H
    end

    subgraph Output
        I[最终检测结果]
    end

    E -- 明确 --> I
    E -- 模糊/疑难 --> F
    E -- 模糊/疑难 --> G
    H --> I

    subgraph Core Components
        J[增强歧义检测器]
        K[向量数据库 ChromaDB]
        L[动态权重学习器]
    end

    E -.-> J
    G -.-> K
    E -.-> L
```

## 2. 核心模块与技术详解

### 2.1. 第一层：快速筛选层 (Layer 1)

第一层由两个并行的、专门化的智能体组成，负责对输入文本进行快速、低成本的初步判断。

#### 2.1.1. `ToxiGenRoBERTaAgent`

- **技术实现**: 基于 `transformers` 库，加载了一个在 **ToxiGen** 数据集上微调过的 `RoBERTa` 模型。
- **模型路径**: `D:/models/toxigen_roberta`
- **核心作用**: 这是一个强大的深度学习模型，经过对抗性训练，对**隐式和显式**的有害言论都有很高的识别能力。它能输出一个明确的判断（`verdict`）和对应的置信度（`confidence`）。
- **关键参数**:
    - `max_length`: 512 (输入文本最大长度)
    - `device`: "auto" (自动选择 CUDA 或 CPU)

#### 2.1.2. `HurtLexAgent`

- **技术实现**: 基于 `pandas` 和正则表达式，实现了一个基于词典匹配的检测器。
- **词典路径**: `word_dic/hurtlex/hurtlex_EN.tsv`
- **核心作用**: 该智能体使用 **HurtLex** 词典，该词典包含了17个类别的多语言攻击性词汇。它通过匹配文本中的敏感词，并根据不同类别的权重（`category_weights`），计算出一个综合的“攻击性分数”。这是一种非常快速的检测方法，能有效识别包含明确攻击性词汇的文本。
- **关键参数**:
    - `confidence_threshold`: 0.3 (判断为攻击性的分数阈值)
    - `category_weights`: 为17个不同类别的词汇（如种族诽谤、身体残疾、道德缺陷等）分配了不同的权重，使分数计算更精细。

### 2.2. 增强歧义检测器 (Enhanced Ambiguity Detector)

这是连接第一层和第二层的核心枢纽，决定了系统的工作流向。

- **技术实现**: `core_modules/features/enhanced_ambiguity_detector.py`
- **核心作用**: 它综合运用多种策略来计算一个文本的“歧义分数”，如果该分数超过阈值，则将文本送入第二层分析。
- **歧义检测策略**:
    1.  **置信度分析**: 检查L1各智能体输出的置信度是否处于模糊区间（0.3-0.7），或置信度方差是否过大。
    2.  **模型分歧**: 检查`ToxiGenRoBERTaAgent`和`HurtLexAgent`的判断结果是否不一致。
    3.  **文本特征异常**: 利用`TextFeatureExtractor`提取文本的深层语言特征（如情感、否定词、大写字母比例、泛化表达等），判断是否存在可能导致误判的异常组合（如讽刺）。
    4.  **隐式仇恨模式**: 专门查找难以直接识别的模式，如间接指代（“那些人”）、文化暗示（“urban culture”）等。
- **关键参数**:
    - `ambiguity_threshold`: 0.5 (综合歧义分数的触发阈值)
    - `dataset_specific_weights`: 针对不同数据集（如`ImplicitHate`）的特点，为上述不同策略动态分配权重，使得歧义判断更具适应性。

### 2.3. 第二层：精细分析层 (Layer 2)

当第一层判定文本为“模糊”或“疑难”时，第二层启动，使用大语言模型（LLM）进行深度分析。

- **模型支持**: 框架支持多种LLM，包括通过API调用的`OpenAI GPT`系列、`Claude`系列，以及本地部署的`ChatGLM`、`Qwen`等模型，展示了高度的灵活性和可扩展性。

#### 2.3.1. `SingleAgentDetector`

- **技术实现**: `offensive_speech_detection/models.py`
- **核心作用**: 这是一个基础的LLM检测器。它将文本和一段精心设计的指令（System Prompt）发送给LLM，要求LLM判断文本是否包含冒犯性言论，并以**JSON格式**返回判断结果（`verdict`）和解释（`explanation`）。
- **Prompt**: `BASE_PROMPTS["single_agent"]`

#### 2.3.2. `RetrievalAugmentedDetector` (RAG)

- **技术实现**: `offensive_speech_detection/models.py`
- **核心作用**: 这是系统的亮点之一，采用了**检索增强生成（Retrieval-Augmented Generation, RAG）**技术。在调用LLM之前，它会先执行以下步骤：
    1.  **向量化查询**: 将输入文本通过`text-embedding-ada-002`模型转换为向量。
    2.  **相似性搜索**: 使用该向量在预先构建好的**ChromaDB向量数据库**中进行搜索。
    3.  **上下文注入**: 检索出N个最相似的、已标注的文本案例（Few-shot Examples）。
    4.  **增强调用**: 将这些案例作为上下文，连同原始文本一起发送给LLM，让LLM在有“参考案例”的情况下做出更准确的判断。
- **向量数据库**:
    - **技术**: `ChromaDB`
    - **存储路径**: `./vector_db`
    - **构建方式**: 为每个数据集（如`HateSpeechOffensive`）的训练集单独构建一个向量数据库。
- **Prompt**: `BASE_PROMPTS["retrieval_agent"]`

### 2.4. 决策与融合 (Decision Fusion)

系统在多个环节应用了决策融合机制。

- **第一层融合**: `DecisionFusion`模块融合`ToxiGen`和`HurtLex`的结果。它并非简单的投票，而是设计了复杂的逻辑，例如：如果`ToxiGen`的置信度非常高，则直接采纳其结果。
- **最终融合**: 如果第二层被触发，系统会综合考虑第一层和第二层两个LLM智能体的所有输出，通过一个最终的融合逻辑（例如，若LLM强一致性，则采纳LLM结果；若完全分歧，则采用多数投票）得出最终结论。

## 3. 性能优化与高级功能

### 3.1. 动态权重学习

- **技术实现**: `core_modules/optimization/layer1_weight_learner.py`
- **功能**: 系统包含一个可选的动态权重学习模块。如果启用 (`enable_dynamic_weights: True`)，系统可以根据真实的标注数据（`true_label`），使用简单的强化学习思想（正确预测则奖励，错误则惩罚），动态调整第一层`ToxiGen`和`HurtLex`的权重，从而使系统能够自适应特定数据集的言论分布。
- **关键参数**:
    - `weight_learning_rate`: 0.01 (学习率)
    - `weight_performance_window`: 50 (用于评估性能的样本窗口大小)

### 3.2. 性能优化器

- **技术实现**: `core_modules/optimization/performance_optimizer.py`
- **功能**: 为了提高系统的处理效率，集成了一个性能优化器，提供三大功能：
    1.  **缓存 (Caching)**: 将已处理文本的最终结果缓存起来，避免重复计算。支持内存缓存和可选的磁盘缓存。
    2.  **并行处理 (Parallel Processing)**: 使用`ThreadPoolExecutor`并行执行第一层和第二层中的多个智能体，显著缩短了处理时间。
    3.  **容错 (Fault Tolerance)**: 包含了重试机制和断路器模式，以应对可能的模型调用失败或API错误。

## 4. 数据与评估

- **数据集**: 系统设计用于处理多个经典的仇恨言论数据集，包括 `HateSpeechOffensive`, `HateSpeechStormfront`, 和 `ImplicitHate`。这些数据集的路径位于 `datasets/` 目录下。
- **评估脚本**:
    - `run_single_agent_evaluation.py`: 用于评估单个LLM智能体的性能。
    - `run_multi_agent_evaluation.py`: 用于评估整个双层多智能体框架的性能。
    - `run_comparison_evaluation.py`: 用于对比单个智能体和多智能体框架的性能。
- **结果与日志**:
    - **日志**: 详细的JSON格式日志文件保存在 `logs/` 目录中，记录了每一次检测的详细过程和结果。
    - **图表**: 评估结果的可视化图表（如混淆矩阵、性能对比图）保存在 `results/` 目录中。

## 5. 总结与展望

本框架通过其创新的**双层架构**、**智能歧义检测**和**检索增强（RAG）**技术，构建了一个强大、高效且灵活的仇恨言论检测系统。它不仅能够处理显式的攻击性言论，更在识别隐式、模糊的仇恨言论方面展现了巨大潜力。

未来的工作可以从以下几个方面展开：
- **模型扩展**: 集成更多、更先进的本地或云端大语言模型。
- **权重学习优化**: 采用更复杂的在线学习算法来优化权重。
- **知识图谱**: 引入知识图谱来增强对文化背景和俚语的理解。

---
这份报告全面地分析了您项目的技术实现、系统架构和核心参数。如果您需要针对某个特定模块进行更深入的探讨，请随时提出。 