#!/usr/bin/env python3
"""
日志分析脚本 - 分析双层检测系统的性能表现
"""

import json
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns

def load_log_file(log_path: str) -> Dict[str, Any]:
    """加载日志文件"""
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {log_path}: {e}")
        return {}

def analyze_performance_metrics(log_data: Dict[str, Any]) -> Dict[str, Any]:
    """分析性能指标"""
    # 优先使用metrics部分的数据
    metrics = log_data.get('metrics', {})
    if metrics:
        return {
            'total_samples': log_data.get('metadata', {}).get('num_samples', 0),
            'accuracy': metrics.get('accuracy', 0),
            'precision': metrics.get('precision', 0),
            'recall': metrics.get('recall', 0),
            'f1_score': metrics.get('f1', 0),
            'confusion_matrix': metrics.get('confusion_matrix', []),
            'avg_processing_time': metrics.get('avg_processing_time', 0)
        }

    # 如果没有metrics，从results计算
    results = log_data.get('results', [])
    if not results:
        return {}

    # 基本统计
    total_samples = len(results)
    correct_predictions = sum(1 for r in results if r.get('verdict') == r.get('true_label'))
    accuracy = correct_predictions / total_samples if total_samples > 0 else 0

    # 分类统计
    true_positives = sum(1 for r in results if r.get('verdict') == 1 and r.get('true_label') == 1)
    false_positives = sum(1 for r in results if r.get('verdict') == 1 and r.get('true_label') == 0)
    true_negatives = sum(1 for r in results if r.get('verdict') == 0 and r.get('true_label') == 0)
    false_negatives = sum(1 for r in results if r.get('verdict') == 0 and r.get('true_label') == 1)

    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

    return {
        'total_samples': total_samples,
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'true_positives': true_positives,
        'false_positives': false_positives,
        'true_negatives': true_negatives,
        'false_negatives': false_negatives
    }

def analyze_layer_usage(log_data: Dict[str, Any]) -> Dict[str, Any]:
    """分析层级使用情况"""
    results = log_data.get('results', [])
    if not results:
        return {}

    layer1_only = 0
    layer2_used = 0
    decision_paths = Counter()
    ambiguity_reasons = Counter()

    for result in results:
        # 检查是否触发了第二层
        layer2_triggered = result.get('layer2_triggered', False)

        if layer2_triggered:
            layer2_used += 1
        else:
            layer1_only += 1

        # 分析决策路径
        reasoning = result.get('reasoning', '')
        if reasoning:
            decision_paths[reasoning] += 1

        # 从layer1_results获取决策路径
        layer1_results = result.get('layer1_results', {})
        fusion_result = layer1_results.get('fusion_result', {})
        layer1_decision_path = fusion_result.get('decision_path', '')
        if layer1_decision_path:
            decision_paths[layer1_decision_path] += 1

        # 分析歧义原因
        ambiguity_reasons_list = result.get('ambiguity_reasons', [])
        for reason in ambiguity_reasons_list:
            ambiguity_reasons[reason] += 1

    total = len(results)
    return {
        'layer1_only_count': layer1_only,
        'layer2_used_count': layer2_used,
        'layer1_only_percentage': layer1_only / total * 100 if total > 0 else 0,
        'layer2_used_percentage': layer2_used / total * 100 if total > 0 else 0,
        'decision_paths': dict(decision_paths),
        'ambiguity_reasons': dict(ambiguity_reasons)
    }

def analyze_strategy_effectiveness(log_data: Dict[str, Any]) -> Dict[str, Any]:
    """分析策略有效性"""
    results = log_data.get('results', [])
    if not results:
        return {}

    strategy_performance = defaultdict(lambda: {'correct': 0, 'total': 0})

    for result in results:
        # 获取决策路径
        reasoning = result.get('reasoning', '')
        layer1_results = result.get('layer1_results', {})
        fusion_result = layer1_results.get('fusion_result', {})
        layer1_decision_path = fusion_result.get('decision_path', '')

        # 使用reasoning作为主要策略标识，layer1_decision_path作为补充
        strategy = reasoning if reasoning else layer1_decision_path

        # 这里我们无法直接判断正确性，因为日志中没有true_label
        # 但我们可以统计各种策略的使用频率
        strategy_performance[strategy]['total'] += 1

    # 计算策略使用比例
    total_samples = len(results)
    strategy_usage = {}
    for strategy, stats in strategy_performance.items():
        if stats['total'] > 0:
            strategy_usage[strategy] = stats['total'] / total_samples

    return {
        'strategy_performance': dict(strategy_performance),
        'strategy_usage': strategy_usage
    }

def analyze_confidence_distribution(log_data: Dict[str, Any]) -> Dict[str, Any]:
    """分析置信度分布"""
    results = log_data.get('results', [])
    if not results:
        return {}
    
    confidences = []
    correct_confidences = []
    incorrect_confidences = []
    
    for result in results:
        confidence = result.get('confidence', 0.5)
        confidences.append(confidence)
        
        if result.get('prediction') == result.get('true_label'):
            correct_confidences.append(confidence)
        else:
            incorrect_confidences.append(confidence)
    
    return {
        'mean_confidence': np.mean(confidences) if confidences else 0,
        'std_confidence': np.std(confidences) if confidences else 0,
        'mean_correct_confidence': np.mean(correct_confidences) if correct_confidences else 0,
        'mean_incorrect_confidence': np.mean(incorrect_confidences) if incorrect_confidences else 0,
        'confidence_distribution': {
            'low_confidence': sum(1 for c in confidences if c < 0.3),
            'medium_confidence': sum(1 for c in confidences if 0.3 <= c <= 0.7),
            'high_confidence': sum(1 for c in confidences if c > 0.7)
        }
    }

def analyze_model_agreement(log_data: Dict[str, Any]) -> Dict[str, Any]:
    """分析模型一致性"""
    results = log_data.get('results', [])
    if not results:
        return {}

    toxigen_hurtlex_agreement = 0
    layer1_layer2_agreement = 0
    total_with_layer2 = 0
    single_retrieval_agreement = 0

    for result in results:
        # 分析ToxiGen和HurtLex的一致性
        layer1_results = result.get('layer1_results', {})
        toxigen_verdict = layer1_results.get('toxigen_result', {}).get('verdict')
        hurtlex_verdict = layer1_results.get('hurtlex_result', {}).get('verdict')

        if toxigen_verdict is not None and hurtlex_verdict is not None:
            if toxigen_verdict == hurtlex_verdict:
                toxigen_hurtlex_agreement += 1

        # 分析第一层和第二层的一致性
        layer1_verdict = layer1_results.get('fusion_result', {}).get('verdict')
        layer2_results = result.get('layer2_results')

        if layer2_results:
            total_with_layer2 += 1
            single_result = layer2_results.get('single_result', {})
            retrieval_result = layer2_results.get('retrieval_result', {})

            single_verdict = single_result.get('verdict') if single_result else None
            retrieval_verdict = retrieval_result.get('verdict') if retrieval_result else None

            # 第一层和第二层一致性
            if layer1_verdict is not None and single_verdict is not None:
                if layer1_verdict == single_verdict:
                    layer1_layer2_agreement += 1

            # 第二层内部一致性（single vs retrieval）
            if single_verdict is not None and retrieval_verdict is not None:
                if single_verdict == retrieval_verdict:
                    single_retrieval_agreement += 1

    total = len(results)
    return {
        'toxigen_hurtlex_agreement_rate': toxigen_hurtlex_agreement / total * 100 if total > 0 else 0,
        'layer1_layer2_agreement_rate': layer1_layer2_agreement / total_with_layer2 * 100 if total_with_layer2 > 0 else 0,
        'single_retrieval_agreement_rate': single_retrieval_agreement / total_with_layer2 * 100 if total_with_layer2 > 0 else 0,
        'total_samples': total,
        'samples_with_layer2': total_with_layer2
    }

def generate_comprehensive_report(logs_dir: str = 'logs') -> None:
    """生成综合分析报告"""
    log_files = [f for f in os.listdir(logs_dir) if f.endswith('.json')]

    print("=" * 80)
    print("双层检测系统日志分析报告")
    print("=" * 80)

    for log_file in log_files:
        log_path = os.path.join(logs_dir, log_file)
        log_data = load_log_file(log_path)

        if not log_data:
            continue

        # 从metadata获取信息
        metadata = log_data.get('metadata', {})
        dataset_name = metadata.get('dataset', 'Unknown')
        model_name = metadata.get('model', 'Unknown')
        
        print(f"\n{'='*60}")
        print(f"数据集: {dataset_name}")
        print(f"模型: {model_name}")
        print(f"日志文件: {log_file}")
        print(f"{'='*60}")
        
        # 性能指标分析
        performance = analyze_performance_metrics(log_data)
        print(f"\n📊 性能指标:")
        print(f"  总样本数: {performance.get('total_samples', 0)}")
        print(f"  准确率: {performance.get('accuracy', 0):.4f}")
        print(f"  精确率: {performance.get('precision', 0):.4f}")
        print(f"  召回率: {performance.get('recall', 0):.4f}")
        print(f"  F1分数: {performance.get('f1_score', 0):.4f}")
        
        # 层级使用分析
        layer_usage = analyze_layer_usage(log_data)
        print(f"\n🏗️ 层级使用情况:")
        print(f"  仅使用第一层: {layer_usage.get('layer1_only_count', 0)} ({layer_usage.get('layer1_only_percentage', 0):.1f}%)")
        print(f"  使用第二层: {layer_usage.get('layer2_used_count', 0)} ({layer_usage.get('layer2_used_percentage', 0):.1f}%)")
        
        # 决策路径分析
        decision_paths = layer_usage.get('decision_paths', {})
        print(f"\n🛤️ 主要决策路径:")
        for path, count in sorted(decision_paths.items(), key=lambda x: x[1], reverse=True)[:5]:
            percentage = count / performance.get('total_samples', 1) * 100
            print(f"  {path}: {count} ({percentage:.1f}%)")
        
        # 歧义原因分析
        ambiguity_reasons = layer_usage.get('ambiguity_reasons', {})
        if ambiguity_reasons:
            print(f"\n🤔 主要歧义原因:")
            for reason, count in sorted(ambiguity_reasons.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  {reason}: {count}")
        
        # 策略有效性分析
        strategy_effectiveness = analyze_strategy_effectiveness(log_data)
        strategy_usage = strategy_effectiveness.get('strategy_usage', {})
        print(f"\n🎯 策略使用情况:")
        for strategy, usage in sorted(strategy_usage.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {strategy}: {usage:.4f} ({usage*100:.1f}%)")
        
        # 置信度分析
        confidence_analysis = analyze_confidence_distribution(log_data)
        print(f"\n📈 置信度分析:")
        print(f"  平均置信度: {confidence_analysis.get('mean_confidence', 0):.4f}")
        print(f"  正确预测平均置信度: {confidence_analysis.get('mean_correct_confidence', 0):.4f}")
        print(f"  错误预测平均置信度: {confidence_analysis.get('mean_incorrect_confidence', 0):.4f}")
        
        # 模型一致性分析
        agreement_analysis = analyze_model_agreement(log_data)
        print(f"\n🤝 模型一致性:")
        print(f"  ToxiGen-HurtLex一致率: {agreement_analysis.get('toxigen_hurtlex_agreement_rate', 0):.1f}%")
        print(f"  第一层-第二层一致率: {agreement_analysis.get('layer1_layer2_agreement_rate', 0):.1f}%")
        print(f"  第二层内部一致率: {agreement_analysis.get('single_retrieval_agreement_rate', 0):.1f}%")
        print(f"  使用第二层的样本数: {agreement_analysis.get('samples_with_layer2', 0)}")

if __name__ == "__main__":
    generate_comprehensive_report()
