#!/usr/bin/env python3
"""
两层多智能体仇恨言论检测系统
Two-Layer Multi-Agent Hate Speech Detection System

基于实际测试结果设计的两层架构：
- 第一层：快速筛选层（ToxiGen + HurtLex）
- 第二层：精细分析层（LLM智能体）

作者: AI Assistant
日期: 2025-08-02
版本: 1.0
"""

import time
import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入智能体
from core_modules.agents import (
    ToxiGenRoBERTaAgent, ToxiGenRoBERTaConfig,
    TwitterSentimentAgent, TwitterSentimentConfig,
    HurtLexAgent, HurtLexConfig
)
from offensive_speech_detection.models import (
    SingleAgentDetector, RetrievalAugmentedDetector,
    VectorDatabaseManager
)
from core_modules.features import (
    EnhancedAmbiguityDetector, AmbiguityConfig,
    TextFeatureExtractor, FeatureConfig
)
from core_modules.optimization import (
    PerformanceOptimizer, CacheConfig, ParallelConfig, FaultToleranceConfig
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TwoLayerConfig:
    """两层架构配置"""
    # 第一层配置 - 优化后的参数
    layer1_toxigen_weight: float = 0.8  # 提高ToxiGen权重
    layer1_hurtlex_weight: float = 0.2  # 降低HurtLex权重
    layer1_confidence_threshold_high: float = 0.8  # 提高高置信度阈值
    layer1_confidence_threshold_low: float = 0.3   # 降低低置信度阈值
    layer1_consensus_threshold: float = 0.5        # 降低一致性阈值

    # 歧义检测配置 - 缩小歧义范围
    ambiguity_confidence_range: Tuple[float, float] = (0.4, 0.6)
    enable_disagreement_detection: bool = True
    enable_implicit_hate_detection: bool = True
    
    # 第二层配置
    layer2_llm_model: str = "gpt-3.5-turbo-0125"  # 模型名称（用于日志）
    layer2_provider: str = "api"
    layer2_local_model_path: str = None  # 本地模型完整路径（仅当provider为local时使用）
    layer2_timeout: float = 30.0
    
    # 系统配置
    enable_caching: bool = True
    enable_parallel_processing: bool = True
    max_workers: int = 3

    # 性能优化配置
    enable_performance_optimization: bool = True
    cache_size: int = 1000
    enable_fault_tolerance: bool = True

    # 学术规范配置
    log_detailed_results: bool = True
    save_intermediate_results: bool = True

    # 动态权重学习配置
    enable_dynamic_weights: bool = False  # 启用动态权重学习
    weight_learning_rate: float = 0.01    # 权重学习率
    weight_min_samples: int = 5           # 开始学习的最小样本数
    weight_performance_window: int = 50   # 性能历史窗口大小
    weight_min_weight: float = 0.05       # 最小权重限制
    weight_max_weight: float = 0.95       # 最大权重限制
    weight_enable_adaptive_lr: bool = True # 启用自适应学习率
    weight_lr_decay_factor: float = 0.95  # 学习率衰减因子
    weight_lr_increase_factor: float = 1.05 # 学习率增长因子
    weight_reward_correct: float = 0.5    # 正确预测奖励
    weight_penalty_incorrect: float = -0.5 # 错误预测惩罚
    weight_log_updates: bool = True       # 记录权重更新日志
    weight_update_log_interval: int = 10  # 权重更新日志间隔
    weight_performance_window: int = 50   # 性能历史窗口大小
    weight_min_weight: float = 0.05       # 最小权重限制
    weight_max_weight: float = 0.95       # 最大权重限制
    weight_enable_adaptive_lr: bool = True # 启用自适应学习率
    weight_lr_decay_factor: float = 0.95  # 学习率衰减因子
    weight_lr_increase_factor: float = 1.05 # 学习率增长因子
    weight_reward_correct: float = 0.5    # 正确预测奖励
    weight_penalty_incorrect: float = -0.5 # 错误预测惩罚
    weight_log_updates: bool = True       # 记录权重更新日志
    weight_update_log_interval: int = 10  # 权重更新日志间隔

@dataclass
class DetectionResult:
    """检测结果数据结构"""
    # 最终结果
    verdict: int  # 0: 非仇恨言论, 1: 仇恨言论
    confidence: float
    processing_time: float

    # 第一层结果
    layer1_result: Dict[str, Any] = field(default_factory=dict)
    layer1_toxigen_result: Dict[str, Any] = field(default_factory=dict)
    layer1_hurtlex_result: Dict[str, Any] = field(default_factory=dict)

    # 歧义检测结果
    is_ambiguous: bool = False
    ambiguity_reasons: List[str] = field(default_factory=list)
    ambiguity_score: float = 0.0

    # 第二层结果（如果触发）
    layer2_triggered: bool = False
    layer2_single_result: Optional[Dict[str, Any]] = None
    layer2_retrieval_result: Optional[Dict[str, Any]] = None

    # 决策过程
    decision_path: str = ""
    fusion_details: Dict[str, Any] = field(default_factory=dict)

    # 元数据
    text: str = ""
    timestamp: float = field(default_factory=time.time)

    def copy(self):
        """创建结果副本"""
        import copy as copy_module
        return copy_module.deepcopy(self)

class AmbiguityDetector:
    """歧义检测器"""
    
    def __init__(self, config: TwoLayerConfig):
        self.config = config
        
    def detect_ambiguity(self, toxigen_result: Dict, hurtlex_result: Dict, text: str) -> Tuple[bool, List[str]]:
        """
        检测是否存在歧义
        
        Returns:
            (is_ambiguous, reasons)
        """
        reasons = []
        
        # 策略1：置信度阈值检测
        toxigen_confidence = toxigen_result.get('confidence', 0.5)
        if self.config.ambiguity_confidence_range[0] <= toxigen_confidence <= self.config.ambiguity_confidence_range[1]:
            reasons.append(f"ToxiGen置信度在歧义范围内: {toxigen_confidence:.3f}")
        
        # 策略2：模型分歧检测
        if self.config.enable_disagreement_detection:
            toxigen_verdict = toxigen_result.get('verdict', 0)
            hurtlex_verdict = hurtlex_result.get('verdict', 0)
            if toxigen_verdict != hurtlex_verdict:
                reasons.append(f"模型预测分歧: ToxiGen={toxigen_verdict}, HurtLex={hurtlex_verdict}")
        
        # 策略3：隐式仇恨检测
        if self.config.enable_implicit_hate_detection:
            if self._detect_implicit_patterns(text, toxigen_result, hurtlex_result):
                reasons.append("检测到潜在隐式仇恨模式")
        
        is_ambiguous = len(reasons) > 0
        return is_ambiguous, reasons
    
    def _detect_implicit_patterns(self, text: str, toxigen_result: Dict, hurtlex_result: Dict) -> bool:
        """检测隐式仇恨模式"""
        # 简化的隐式模式检测
        text_lower = text.lower()
        
        # 检测间接指代模式
        indirect_indicators = [
            "某些人", "这些人", "那些人", "他们总是", "typical", "always", "never"
        ]
        has_indirect = any(indicator in text_lower for indicator in indirect_indicators)
        
        # 检测讽刺模式（基于情绪不一致）
        has_sarcasm = False
        if 'detailed_sentiments' in toxigen_result:
            # 如果有混合情绪信号可能是讽刺
            sentiments = toxigen_result['detailed_sentiments']
            if len(sentiments) > 1:
                scores = [score for _, score in sentiments]
                if max(scores) - min(scores) > 0.3:  # 情绪分数差异较大
                    has_sarcasm = True
        
        # 检测文化暗示（基于特定词汇组合）
        cultural_indicators = [
            ("urban", "culture"), ("inner", "city"), ("welfare", "queen"),
            ("traditional", "values"), ("real", "american")
        ]
        has_cultural_hint = any(
            all(word in text_lower for word in pair) 
            for pair in cultural_indicators
        )
        
        return has_indirect or has_sarcasm or has_cultural_hint

class DecisionFusion:
    """决策融合器 - 支持动态权重学习"""

    def __init__(self, config: TwoLayerConfig, weight_learner=None):
        self.config = config
        self.weight_learner = weight_learner  # 可选的权重学习器
        
    def fuse_layer1_results(self, toxigen_result: Dict, hurtlex_result: Dict, true_label: int = None) -> Dict[str, Any]:
        """融合第一层结果 - 支持动态权重学习"""
        toxigen_verdict = toxigen_result.get('verdict', 0)
        toxigen_confidence = toxigen_result.get('confidence', 0.5)
        hurtlex_verdict = hurtlex_result.get('verdict', 0)
        hurtlex_confidence = hurtlex_result.get('confidence', 0.5)

        # 获取权重：优先使用动态权重，否则使用配置中的固定权重
        if self.weight_learner is not None:
            w1, w2 = self.weight_learner.get_current_weights()
            weight_source = "dynamic"
        else:
            w1, w2 = self.config.layer1_toxigen_weight, self.config.layer1_hurtlex_weight
            weight_source = "static"
        
        # 计算加权置信度
        weighted_confidence = (w1 * toxigen_confidence + w2 * hurtlex_confidence)
        
        # 优化后的决策逻辑 - 优先使用ToxiGen的高置信度判断
        if toxigen_confidence >= self.config.layer1_confidence_threshold_high:
            # ToxiGen高置信度判断
            if toxigen_verdict == 1:
                # 高置信度仇恨言论
                verdict = 1
                confidence = toxigen_confidence
                decision_path = "Layer1_ToxiGen_High_Confidence_Hate"
            elif toxigen_verdict == 0 and hurtlex_verdict == 0:
                # 高置信度非仇恨言论且HurtLex也同意
                verdict = 0
                confidence = toxigen_confidence
                decision_path = "Layer1_ToxiGen_High_Confidence_NonHate"
            else:
                # ToxiGen高置信度但与HurtLex分歧，仍然相信ToxiGen
                verdict = toxigen_verdict
                confidence = toxigen_confidence * 0.9  # 略微降低置信度
                decision_path = "Layer1_ToxiGen_High_Confidence_Override"
        elif (toxigen_confidence >= self.config.layer1_consensus_threshold and
              toxigen_verdict == hurtlex_verdict):
            # 中等置信度但两个模型一致
            verdict = toxigen_verdict
            confidence = weighted_confidence
            decision_path = f"Layer1_Consensus_{'Hate' if toxigen_verdict == 1 else 'NonHate'}"
        elif (toxigen_confidence <= self.config.layer1_confidence_threshold_low and
              toxigen_verdict == 0 and hurtlex_verdict == 0):
            # 低置信度但两个模型都认为不是仇恨言论
            verdict = 0
            confidence = max(weighted_confidence, 0.6)
            decision_path = "Layer1_Low_Confidence_NonHate"
        else:
            # 存在歧义，需要第二层
            verdict = -1  # 表示需要第二层
            confidence = weighted_confidence
            decision_path = "Layer1_Ambiguous"
        
        # 如果有真实标签且启用了权重学习，更新权重
        weight_updated = False
        if self.weight_learner is not None and true_label is not None:
            weight_updated = self.weight_learner.update_weights(
                toxigen_result, hurtlex_result, true_label
            )

        return {
            'verdict': verdict,
            'confidence': confidence,
            'weighted_confidence': weighted_confidence,
            'decision_path': decision_path,
            'toxigen_weight': w1,
            'hurtlex_weight': w2,
            'weight_source': weight_source,
            'weight_updated': weight_updated
        }
    
    def fuse_final_results(self, layer1_result: Dict, layer2_results: Optional[Dict] = None) -> Dict[str, Any]:
        """融合最终结果"""
        if layer2_results is None:
            # 只有第一层结果
            return {
                'verdict': layer1_result['verdict'],
                'confidence': layer1_result['confidence'],
                'decision_path': layer1_result['decision_path'],
                'fusion_method': 'layer1_only'
            }
        
        # 融合两层结果
        layer1_verdict = layer1_result['verdict']
        single_verdict = layer2_results.get('single_result', {}).get('verdict', 0)
        retrieval_verdict = layer2_results.get('retrieval_result', {}).get('verdict', 0)
        
        # LLM共识检查
        llm_consensus = single_verdict + retrieval_verdict >= 1  # 至少一个LLM认为是仇恨言论
        llm_strong_consensus = single_verdict == retrieval_verdict == 1  # 两个LLM都认为是仇恨言论
        
        # 最终决策逻辑
        if layer1_verdict == 1 and llm_consensus:
            # 第一层和LLM都倾向于仇恨言论
            verdict = 1
            confidence = 0.8 if llm_strong_consensus else 0.7
            decision_path = "Layer1_Layer2_Consensus_Hate"
        elif layer1_verdict == 0 and not llm_consensus:
            # 第一层和LLM都倾向于非仇恨言论
            verdict = 0
            confidence = 0.8
            decision_path = "Layer1_Layer2_Consensus_NonHate"
        elif llm_strong_consensus:
            # LLM强一致性，优先采用LLM结果
            verdict = 1
            confidence = 0.75
            decision_path = "Layer2_Strong_Consensus_Override"
        elif not llm_consensus and layer1_verdict == 0:
            # LLM不一致且第一层倾向非仇恨，采用保守策略
            verdict = 0
            confidence = 0.6
            decision_path = "Conservative_NonHate"
        else:
            # 完全分歧，采用多数投票
            total_votes = layer1_verdict + single_verdict + retrieval_verdict
            verdict = 1 if total_votes >= 2 else 0
            confidence = 0.5
            decision_path = "Majority_Vote"
        
        return {
            'verdict': verdict,
            'confidence': confidence,
            'decision_path': decision_path,
            'fusion_method': 'two_layer',
            'llm_consensus': llm_consensus,
            'llm_strong_consensus': llm_strong_consensus,
            'vote_distribution': {
                'layer1': layer1_verdict,
                'single_llm': single_verdict,
                'retrieval_llm': retrieval_verdict,
                'total': layer1_verdict + single_verdict + retrieval_verdict
            }
        }

class TwoLayerDetectionSystem:
    """两层多智能体仇恨言论检测系统"""
    
    def __init__(self, config: TwoLayerConfig = None, vector_db_manager: VectorDatabaseManager = None):
        """
        初始化两层检测系统
        
        Args:
            config: 系统配置
            vector_db_manager: 向量数据库管理器（用于检索增强）
        """
        self.config = config or TwoLayerConfig()
        self.vector_db_manager = vector_db_manager

        # 设置模型名称（用于日志）- 使用LLM模型名称
        self.model = self.config.layer2_llm_model
        
        # 初始化组件
        self._initialize_layer1_agents()
        self._initialize_layer2_agents()
        self._initialize_utilities()
        
        # 性能统计
        self.stats = {
            'total_processed': 0,
            'layer1_only': 0,
            'layer2_triggered': 0,
            'total_processing_time': 0.0,
            'layer1_processing_time': 0.0,
            'layer2_processing_time': 0.0
        }
        
        logger.info("两层多智能体检测系统初始化完成")
    
    def _initialize_layer1_agents(self):
        """初始化第一层智能体"""
        try:
            # ToxiGen智能体
            toxigen_config = ToxiGenRoBERTaConfig()
            self.toxigen_agent = ToxiGenRoBERTaAgent(toxigen_config)
            logger.info("✅ ToxiGen智能体初始化成功")
        except Exception as e:
            logger.error(f"❌ ToxiGen智能体初始化失败: {e}")
            self.toxigen_agent = None
            
        try:
            # HurtLex智能体
            hurtlex_config = HurtLexConfig()
            self.hurtlex_agent = HurtLexAgent(hurtlex_config)
            logger.info("✅ HurtLex智能体初始化成功")
        except Exception as e:
            logger.error(f"❌ HurtLex智能体初始化失败: {e}")
            self.hurtlex_agent = None
    
    def _initialize_layer2_agents(self):
        """初始化第二层智能体"""
        try:
            # 单一LLM智能体
            self.single_llm_agent = SingleAgentDetector(
                model=self.config.layer2_llm_model,
                provider=self.config.layer2_provider,
                local_model_path=self.config.layer2_local_model_path
            )
            logger.info("✅ 单一LLM智能体初始化成功")
        except Exception as e:
            logger.error(f"❌ 单一LLM智能体初始化失败: {e}")
            self.single_llm_agent = None
            
        try:
            # 检索增强LLM智能体
            self.retrieval_llm_agent = RetrievalAugmentedDetector(
                model=self.config.layer2_llm_model,
                provider=self.config.layer2_provider,
                vector_db_manager=self.vector_db_manager,
                local_model_path=self.config.layer2_local_model_path
            )
            logger.info("✅ 检索增强LLM智能体初始化成功")
        except Exception as e:
            logger.error(f"❌ 检索增强LLM智能体初始化失败: {e}")
            self.retrieval_llm_agent = None
    
    def _initialize_utilities(self):
        """初始化工具组件"""
        # 使用增强的歧义检测器
        ambiguity_config = AmbiguityConfig()
        self.ambiguity_detector = EnhancedAmbiguityDetector(ambiguity_config)

        # 初始化权重学习器（如果启用）
        self.weight_learner = None
        if self.config.enable_dynamic_weights:
            from .layer1_weight_learner import Layer1WeightLearner, Layer1WeightConfig
            weight_config = Layer1WeightConfig(
                learning_rate=self.config.weight_learning_rate,
                min_samples_for_update=self.config.weight_min_samples,
                performance_window=self.config.weight_performance_window,
                min_weight=self.config.weight_min_weight,
                max_weight=self.config.weight_max_weight,
                enable_adaptive_lr=self.config.weight_enable_adaptive_lr,
                lr_decay_factor=self.config.weight_lr_decay_factor,
                lr_increase_factor=self.config.weight_lr_increase_factor,
                reward_correct=self.config.weight_reward_correct,
                penalty_incorrect=self.config.weight_penalty_incorrect,
                log_updates=self.config.weight_log_updates,
                update_log_interval=self.config.weight_update_log_interval
            )
            self.weight_learner = Layer1WeightLearner(weight_config)
            logger.info("✅ 动态权重学习器初始化成功")
            logger.info(f"   学习率: {weight_config.learning_rate}")
            logger.info(f"   最小样本数: {weight_config.min_samples_for_update}")
            logger.info(f"   权重范围: [{weight_config.min_weight}, {weight_config.max_weight}]")

        # 初始化决策融合器（传入权重学习器）
        self.decision_fusion = DecisionFusion(self.config, self.weight_learner)

        # 性能优化器
        if self.config.enable_performance_optimization:
            cache_config = CacheConfig(
                memory_cache_size=self.config.cache_size,
                enable_disk_cache=True
            )
            parallel_config = ParallelConfig(
                max_workers=self.config.max_workers
            )
            fault_tolerance_config = FaultToleranceConfig(
                max_retries=3,
                enable_circuit_breaker=self.config.enable_fault_tolerance
            )

            self.performance_optimizer = PerformanceOptimizer(
                cache_config, parallel_config, fault_tolerance_config
            )
        else:
            self.performance_optimizer = None

        # 传统缓存（向后兼容）
        if self.config.enable_caching and not self.config.enable_performance_optimization:
            self.cache = {}

        # 线程池（如果没有使用性能优化器）
        if self.config.enable_parallel_processing and not self.config.enable_performance_optimization:
            self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
    
    def detect(self, text: str, dataset_name: str = None, true_label: int = None) -> DetectionResult:
        """
        主要检测接口

        Args:
            text: 输入文本
            dataset_name: 数据集名称（用于检索增强）
            true_label: 真实标签（用于权重学习，可选）

        Returns:
            DetectionResult: 检测结果
        """
        start_time = time.time()
        
        # 检查缓存
        cache_key = f"detection_{hash(text)}_{dataset_name or 'default'}"

        if self.performance_optimizer:
            cached_result = self.performance_optimizer.cache.get(cache_key)
            if cached_result is not None:
                cached_result = cached_result.copy()
                cached_result.processing_time = time.time() - start_time
                return cached_result
        elif self.config.enable_caching and hasattr(self, 'cache') and text in self.cache:
            cached_result = self.cache[text].copy()
            cached_result.processing_time = time.time() - start_time
            return cached_result
        
        # 创建结果对象
        result = DetectionResult(
            verdict=0,
            confidence=0.0,
            processing_time=0.0,
            text=text
        )
        
        try:
            # 第一层检测
            layer1_start = time.time()
            layer1_result = self._run_layer1(text, true_label)
            layer1_time = time.time() - layer1_start
            
            result.layer1_result = layer1_result['fusion_result']
            result.layer1_toxigen_result = layer1_result['toxigen_result']
            result.layer1_hurtlex_result = layer1_result['hurtlex_result']
            
            # 歧义检测（使用增强检测器）
            model_results_for_ambiguity = {
                'toxigen': layer1_result['toxigen_result'],
                'hurtlex': layer1_result['hurtlex_result']
            }
            is_ambiguous, ambiguity_reasons, ambiguity_score = self.ambiguity_detector.detect_ambiguity(
                text, model_results_for_ambiguity, dataset_name
            )
            
            result.is_ambiguous = is_ambiguous
            result.ambiguity_reasons = ambiguity_reasons
            result.ambiguity_score = ambiguity_score
            
            # 决策分支
            if not is_ambiguous and layer1_result['fusion_result']['verdict'] != -1:
                # 第一层可以直接决策
                result.verdict = layer1_result['fusion_result']['verdict']
                result.confidence = layer1_result['fusion_result']['confidence']
                result.decision_path = layer1_result['fusion_result']['decision_path']
                result.fusion_details = layer1_result['fusion_result']
                
                self.stats['layer1_only'] += 1
            else:
                # 需要第二层分析
                layer2_start = time.time()
                layer2_result = self._run_layer2(text, dataset_name)
                layer2_time = time.time() - layer2_start
                
                result.layer2_triggered = True
                result.layer2_single_result = layer2_result.get('single_result')
                result.layer2_retrieval_result = layer2_result.get('retrieval_result')
                
                # 最终融合
                final_result = self.decision_fusion.fuse_final_results(
                    layer1_result['fusion_result'],
                    layer2_result
                )
                
                result.verdict = final_result['verdict']
                result.confidence = final_result['confidence']
                result.decision_path = final_result['decision_path']
                result.fusion_details = final_result
                
                self.stats['layer2_triggered'] += 1
                self.stats['layer2_processing_time'] += layer2_time
            
            # 更新统计
            total_time = time.time() - start_time
            result.processing_time = total_time
            
            self.stats['total_processed'] += 1
            self.stats['total_processing_time'] += total_time
            self.stats['layer1_processing_time'] += layer1_time
            
            # 缓存结果
            if self.performance_optimizer:
                self.performance_optimizer.cache.put(cache_key, result)
            elif self.config.enable_caching and hasattr(self, 'cache'):
                self.cache[text] = result
            
            return result

        except Exception as e:
            logger.error(f"检测过程出错: {e}")
            result.verdict = 0  # 默认为非仇恨言论
            result.confidence = 0.0
            result.decision_path = "Error_Default"
            result.processing_time = time.time() - start_time
            return result

    def _run_layer1(self, text: str, true_label: int = None) -> Dict[str, Any]:
        """运行第一层检测"""
        if self.config.enable_parallel_processing:
            return self._run_layer1_parallel(text, true_label)
        else:
            return self._run_layer1_sequential(text, true_label)

    def _run_layer1_sequential(self, text: str, true_label: int = None) -> Dict[str, Any]:
        """顺序运行第一层检测"""
        # ToxiGen检测
        toxigen_result = {}
        if self.toxigen_agent:
            try:
                toxigen_result = self.toxigen_agent.detect(text)
            except Exception as e:
                logger.error(f"ToxiGen检测失败: {e}")
                toxigen_result = {'verdict': 0, 'confidence': 0.0, 'error': str(e)}

        # HurtLex检测
        hurtlex_result = {}
        if self.hurtlex_agent:
            try:
                hurtlex_result = self.hurtlex_agent.detect(text)
            except Exception as e:
                logger.error(f"HurtLex检测失败: {e}")
                hurtlex_result = {'verdict': 0, 'confidence': 0.0, 'error': str(e)}

        # 融合结果
        fusion_result = self.decision_fusion.fuse_layer1_results(toxigen_result, hurtlex_result, true_label)

        return {
            'toxigen_result': toxigen_result,
            'hurtlex_result': hurtlex_result,
            'fusion_result': fusion_result
        }

    def _run_layer1_parallel(self, text: str, true_label: int = None) -> Dict[str, Any]:
        """并行运行第一层检测"""
        if self.performance_optimizer:
            # 使用性能优化器的并行处理
            def run_agent(agent_info):
                agent, name = agent_info
                try:
                    return agent.detect(text)
                except Exception as e:
                    logger.error(f"{name}检测失败: {e}")
                    return {'verdict': 0, 'confidence': 0.0, 'error': str(e)}

            agents_info = []
            if self.toxigen_agent:
                agents_info.append((self.toxigen_agent, 'toxigen'))
            if self.hurtlex_agent:
                agents_info.append((self.hurtlex_agent, 'hurtlex'))

            try:
                results_list = self.performance_optimizer.parallel_process(run_agent, agents_info)

                results = {}
                for i, result in enumerate(results_list):
                    agent_name = agents_info[i][1]
                    if result is not None:
                        results[agent_name] = result
                    else:
                        results[agent_name] = {'verdict': 0, 'confidence': 0.0, 'error': 'Parallel execution failed'}

            except Exception as e:
                logger.error(f"并行执行失败: {e}")
                # 降级到顺序执行
                return self._run_layer1_sequential(text, true_label)
        else:
            # 传统并行处理
            if not hasattr(self, 'executor'):
                # 如果没有executor，降级到顺序执行
                return self._run_layer1_sequential(text, true_label)

            futures = {}

            # 提交任务
            if self.toxigen_agent:
                futures['toxigen'] = self.executor.submit(self.toxigen_agent.detect, text)
            if self.hurtlex_agent:
                futures['hurtlex'] = self.executor.submit(self.hurtlex_agent.detect, text)

            # 收集结果
            results = {}
            for name, future in futures.items():
                try:
                    results[name] = future.result(timeout=10.0)  # 10秒超时
                except Exception as e:
                    logger.error(f"{name}检测失败: {e}")
                    results[name] = {'verdict': 0, 'confidence': 0.0, 'error': str(e)}

        toxigen_result = results.get('toxigen', {'verdict': 0, 'confidence': 0.0})
        hurtlex_result = results.get('hurtlex', {'verdict': 0, 'confidence': 0.0})

        # 融合结果
        fusion_result = self.decision_fusion.fuse_layer1_results(toxigen_result, hurtlex_result, true_label)

        return {
            'toxigen_result': toxigen_result,
            'hurtlex_result': hurtlex_result,
            'fusion_result': fusion_result
        }

    def _run_layer2(self, text: str, dataset_name: str = None) -> Dict[str, Any]:
        """运行第二层检测"""
        if self.config.enable_parallel_processing:
            return self._run_layer2_parallel(text, dataset_name)
        else:
            return self._run_layer2_sequential(text, dataset_name)

    def _run_layer2_sequential(self, text: str, dataset_name: str = None) -> Dict[str, Any]:
        """顺序运行第二层检测"""
        results = {}

        # 单一LLM智能体
        if self.single_llm_agent:
            try:
                results['single_result'] = self.single_llm_agent.detect(text)
            except Exception as e:
                logger.error(f"单一LLM智能体检测失败: {e}")
                results['single_result'] = {'verdict': 0, 'confidence': 0.0, 'error': str(e)}

        # 检索增强LLM智能体
        if self.retrieval_llm_agent and dataset_name:
            try:
                results['retrieval_result'] = self.retrieval_llm_agent.detect(text, dataset_name)
            except Exception as e:
                logger.error(f"检索增强LLM智能体检测失败: {e}")
                results['retrieval_result'] = {'verdict': 0, 'confidence': 0.0, 'error': str(e)}

        return results

    def _run_layer2_parallel(self, text: str, dataset_name: str = None) -> Dict[str, Any]:
        """并行运行第二层检测"""
        if self.performance_optimizer:
            # 使用性能优化器的并行处理
            def run_llm_agent(agent_info):
                agent, name, args = agent_info
                try:
                    if name == 'single':
                        return agent.detect(text)
                    elif name == 'retrieval':
                        return agent.detect(text, dataset_name)
                except Exception as e:
                    logger.error(f"第二层{name}智能体检测失败: {e}")
                    return {'verdict': 0, 'confidence': 0.0, 'error': str(e)}

            agents_info = []
            if self.single_llm_agent:
                agents_info.append((self.single_llm_agent, 'single', [text]))
            if self.retrieval_llm_agent and dataset_name:
                agents_info.append((self.retrieval_llm_agent, 'retrieval', [text, dataset_name]))

            try:
                results_list = self.performance_optimizer.parallel_process(run_llm_agent, agents_info)

                results = {}
                for i, result in enumerate(results_list):
                    agent_name = agents_info[i][1]
                    if result is not None:
                        if agent_name == 'single':
                            results['single_result'] = result
                        elif agent_name == 'retrieval':
                            results['retrieval_result'] = result
                    else:
                        error_result = {'verdict': 0, 'confidence': 0.0, 'error': 'Parallel execution failed'}
                        if agent_name == 'single':
                            results['single_result'] = error_result
                        elif agent_name == 'retrieval':
                            results['retrieval_result'] = error_result

            except Exception as e:
                logger.error(f"第二层并行执行失败: {e}")
                # 降级到顺序执行
                return self._run_layer2_sequential(text, dataset_name)
        else:
            # 传统并行处理
            if not hasattr(self, 'executor'):
                # 如果没有executor，降级到顺序执行
                return self._run_layer2_sequential(text, dataset_name)

            futures = {}

            # 提交任务
            if self.single_llm_agent:
                futures['single'] = self.executor.submit(self.single_llm_agent.detect, text)
            if self.retrieval_llm_agent and dataset_name:
                futures['retrieval'] = self.executor.submit(self.retrieval_llm_agent.detect, text, dataset_name)

            # 收集结果
            results = {}
            for name, future in futures.items():
                try:
                    result = future.result(timeout=self.config.layer2_timeout)
                    if name == 'single':
                        results['single_result'] = result
                    elif name == 'retrieval':
                        results['retrieval_result'] = result
                except Exception as e:
                    logger.error(f"第二层{name}智能体检测失败: {e}")
                    error_result = {'verdict': 0, 'confidence': 0.0, 'error': str(e)}
                    if name == 'single':
                        results['single_result'] = error_result
                    elif name == 'retrieval':
                        results['retrieval_result'] = error_result

        return results

    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        total_processed = self.stats['total_processed']
        if total_processed == 0:
            base_stats = self.stats
        else:
            base_stats = {
                **self.stats,
                'layer1_only_rate': self.stats['layer1_only'] / total_processed,
                'layer2_trigger_rate': self.stats['layer2_triggered'] / total_processed,
                'avg_total_time': self.stats['total_processing_time'] / total_processed,
                'avg_layer1_time': self.stats['layer1_processing_time'] / total_processed,
                'avg_layer2_time': (self.stats['layer2_processing_time'] / self.stats['layer2_triggered']
                                   if self.stats['layer2_triggered'] > 0 else 0)
            }

        # 添加性能优化器统计
        if self.performance_optimizer:
            perf_stats = self.performance_optimizer.get_performance_stats()
            base_stats['performance_optimization'] = perf_stats

        return base_stats

    def clear_cache(self):
        """清除缓存"""
        if self.performance_optimizer:
            self.performance_optimizer.cache.clear()
            logger.info("性能优化器缓存已清除")
        elif hasattr(self, 'cache'):
            self.cache.clear()
            logger.info("系统缓存已清除")

    def cleanup(self):
        """清理系统资源"""
        if self.performance_optimizer:
            self.performance_optimizer.cleanup()
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
        logger.info("系统资源已清理")

    def __del__(self):
        """析构函数，清理资源"""
        try:
            self.cleanup()
        except:
            pass
