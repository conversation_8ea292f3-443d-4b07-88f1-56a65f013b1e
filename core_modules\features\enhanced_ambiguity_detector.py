#!/usr/bin/env python3
"""
增强歧义检测器
Enhanced Ambiguity Detector

基于多种策略和特征的智能歧义检测系统
包括置信度分析、模型分歧检测、特征异常检测等

作者: AI Assistant
日期: 2025-08-02
版本: 1.0
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import logging

from .text_feature_extractor import TextFeatureExtractor, FeatureConfig

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AmbiguityConfig:
    """歧义检测配置"""
    # 置信度阈值策略
    confidence_low_threshold: float = 0.3
    confidence_high_threshold: float = 0.7
    confidence_variance_threshold: float = 0.2
    
    # 模型分歧策略
    enable_disagreement_detection: bool = True
    disagreement_weight: float = 1.0
    
    # 特征异常策略
    enable_feature_anomaly_detection: bool = True
    feature_anomaly_weight: float = 0.8
    
    # 隐式仇恨检测策略
    enable_implicit_hate_detection: bool = True
    implicit_hate_weight: float = 0.9
    
    # 综合评分阈值
    ambiguity_threshold: float = 0.5
    
    # 数据集特定配置
    dataset_specific_weights: Dict[str, Dict[str, float]] = None

class EnhancedAmbiguityDetector:
    """增强歧义检测器"""
    
    def __init__(self, config: AmbiguityConfig = None):
        """
        初始化增强歧义检测器
        
        Args:
            config: 歧义检测配置
        """
        self.config = config or AmbiguityConfig()
        
        # 初始化特征提取器
        feature_config = FeatureConfig()
        self.feature_extractor = TextFeatureExtractor(feature_config)
        
        # 设置数据集特定权重
        if self.config.dataset_specific_weights is None:
            self.config.dataset_specific_weights = self._get_default_dataset_weights()
        
        logger.info("增强歧义检测器初始化完成")
    
    def _get_default_dataset_weights(self) -> Dict[str, Dict[str, float]]:
        """获取默认的数据集特定权重"""
        return {
            'HateSpeechStormfront': {
                'confidence_weight': 1.0,
                'disagreement_weight': 0.8,
                'feature_weight': 0.6,
                'implicit_weight': 0.7
            },
            'HateSpeechOffensive': {
                'confidence_weight': 0.9,
                'disagreement_weight': 1.0,
                'feature_weight': 0.7,
                'implicit_weight': 0.5
            },
            'ImplicitHate': {
                'confidence_weight': 0.7,
                'disagreement_weight': 0.9,
                'feature_weight': 1.0,
                'implicit_weight': 1.0
            }
        }
    
    def detect_ambiguity(self, 
                        text: str,
                        model_results: Dict[str, Any],
                        dataset_name: Optional[str] = None) -> Tuple[bool, List[str], float]:
        """
        检测文本是否存在歧义
        
        Args:
            text: 输入文本
            model_results: 模型预测结果字典
            dataset_name: 数据集名称（用于特定权重）
            
        Returns:
            (is_ambiguous, reasons, ambiguity_score)
        """
        reasons = []
        scores = {}
        
        # 提取文本特征
        features = self.feature_extractor.extract_features(text, model_results)
        
        # 策略1：置信度阈值检测
        confidence_score, confidence_reasons = self._detect_confidence_ambiguity(model_results)
        if confidence_score > 0:
            scores['confidence'] = confidence_score
            reasons.extend(confidence_reasons)
        
        # 策略2：模型分歧检测
        if self.config.enable_disagreement_detection:
            disagreement_score, disagreement_reasons = self._detect_disagreement_ambiguity(model_results)
            if disagreement_score > 0:
                scores['disagreement'] = disagreement_score
                reasons.extend(disagreement_reasons)
        
        # 策略3：特征异常检测
        if self.config.enable_feature_anomaly_detection:
            anomaly_score, anomaly_reasons = self._detect_feature_anomaly(features, text)
            if anomaly_score > 0:
                scores['feature_anomaly'] = anomaly_score
                reasons.extend(anomaly_reasons)
        
        # 策略4：隐式仇恨检测
        if self.config.enable_implicit_hate_detection:
            implicit_score, implicit_reasons = self._detect_implicit_hate_patterns(features, text)
            if implicit_score > 0:
                scores['implicit_hate'] = implicit_score
                reasons.extend(implicit_reasons)
        
        # 计算综合歧义分数
        ambiguity_score = self._calculate_ambiguity_score(scores, dataset_name)
        
        # 判断是否存在歧义
        is_ambiguous = ambiguity_score >= self.config.ambiguity_threshold
        
        return is_ambiguous, reasons, ambiguity_score
    
    def _detect_confidence_ambiguity(self, model_results: Dict[str, Any]) -> Tuple[float, List[str]]:
        """检测基于置信度的歧义"""
        reasons = []
        score = 0.0
        
        confidences = []
        for model_name, result in model_results.items():
            if isinstance(result, dict) and 'confidence' in result:
                confidence = result['confidence']
                confidences.append(confidence)
                
                # 检查置信度是否在歧义范围内
                if self.config.confidence_low_threshold <= confidence <= self.config.confidence_high_threshold:
                    reasons.append(f"{model_name}置信度在歧义范围内: {confidence:.3f}")
                    score += 0.5
        
        # 检查置信度方差
        if len(confidences) > 1:
            confidence_variance = np.var(confidences)
            if confidence_variance > self.config.confidence_variance_threshold:
                reasons.append(f"模型置信度方差过大: {confidence_variance:.3f}")
                score += 0.3
        
        # 检查极低置信度
        low_confidence_count = sum(1 for conf in confidences if conf < self.config.confidence_low_threshold)
        if low_confidence_count > 0:
            reasons.append(f"{low_confidence_count}个模型置信度过低")
            score += 0.4
        
        return min(score, 1.0), reasons
    
    def _detect_disagreement_ambiguity(self, model_results: Dict[str, Any]) -> Tuple[float, List[str]]:
        """检测基于模型分歧的歧义"""
        reasons = []
        score = 0.0
        
        verdicts = []
        for model_name, result in model_results.items():
            if isinstance(result, dict) and 'verdict' in result:
                verdicts.append((model_name, result['verdict']))
        
        if len(verdicts) < 2:
            return 0.0, reasons
        
        # 检查预测分歧
        unique_verdicts = set(verdict for _, verdict in verdicts)
        if len(unique_verdicts) > 1:
            hate_count = sum(1 for _, verdict in verdicts if verdict == 1)
            non_hate_count = len(verdicts) - hate_count
            
            reasons.append(f"模型预测分歧: {hate_count}个认为是仇恨言论, {non_hate_count}个认为不是")
            
            # 分歧程度评分
            if hate_count == non_hate_count:
                score = 1.0  # 完全分歧
            else:
                minority_ratio = min(hate_count, non_hate_count) / len(verdicts)
                score = minority_ratio * 2  # 少数派比例越高，分歧越严重
        
        return score, reasons
    
    def _detect_feature_anomaly(self, features: Dict[str, Any], text: str) -> Tuple[float, List[str]]:
        """检测基于特征异常的歧义"""
        reasons = []
        score = 0.0
        
        # 检测矛盾的情绪信号
        if features.get('positive_emotion_count', 0) > 0 and features.get('negative_emotion_count', 0) > 0:
            reasons.append("检测到矛盾的情绪信号（可能是讽刺）")
            score += 0.4
        
        # 检测异常的语言模式
        if features.get('has_indirect_reference', False) and features.get('has_generalization', False):
            reasons.append("检测到间接指代和泛化表达的组合")
            score += 0.5
        
        # 检测异常的结构特征
        if features.get('uppercase_ratio', 0) > 0.3:
            reasons.append("异常高的大写字母比例")
            score += 0.3
        
        if features.get('has_multiple_exclamations', False):
            reasons.append("多个感叹号可能表示强烈情绪")
            score += 0.2
        
        # 检测复杂的语言结构
        if (features.get('negation_count', 0) > 0 and 
            features.get('intensifier_count', 0) > 0 and
            features.get('has_us_them_contrast', False)):
            reasons.append("检测到复杂的语言结构组合")
            score += 0.6
        
        return min(score, 1.0), reasons
    
    def _detect_implicit_hate_patterns(self, features: Dict[str, Any], text: str) -> Tuple[float, List[str]]:
        """检测隐式仇恨模式"""
        reasons = []
        score = 0.0
        
        text_lower = text.lower()
        
        # 检测间接指代模式
        if features.get('has_indirect_reference', False):
            reasons.append("检测到间接指代模式")
            score += 0.4
        
        # 检测文化暗示
        if features.get('has_cultural_hint', False):
            reasons.append("检测到文化暗示模式")
            score += 0.5
        
        # 检测泛化表达
        if features.get('has_generalization', False):
            reasons.append("检测到泛化表达（'总是'、'从不'等）")
            score += 0.3
        
        # 检测"我们"vs"他们"的对立
        if features.get('has_us_them_contrast', False):
            reasons.append("检测到群体对立表达")
            score += 0.4
        
        # 检测委婉表达模式
        euphemism_patterns = [
            'certain people', 'some individuals', 'particular groups',
            'those types', 'that kind', 'such behavior'
        ]
        euphemism_count = sum(1 for pattern in euphemism_patterns if pattern in text_lower)
        if euphemism_count > 0:
            reasons.append(f"检测到{euphemism_count}个委婉表达模式")
            score += euphemism_count * 0.3
        
        # 检测假设性表达
        hypothetical_patterns = ['what if', 'suppose', 'imagine if', 'just saying']
        hypothetical_count = sum(1 for pattern in hypothetical_patterns if pattern in text_lower)
        if hypothetical_count > 0:
            reasons.append("检测到假设性表达（可能是规避责任）")
            score += 0.3
        
        return min(score, 1.0), reasons
    
    def _calculate_ambiguity_score(self, scores: Dict[str, float], dataset_name: Optional[str] = None) -> float:
        """计算综合歧义分数"""
        if not scores:
            return 0.0
        
        # 获取数据集特定权重
        weights = self.config.dataset_specific_weights.get(dataset_name, {}) if dataset_name else {}
        
        # 默认权重
        default_weights = {
            'confidence': 1.0,
            'disagreement': self.config.disagreement_weight,
            'feature_anomaly': self.config.feature_anomaly_weight,
            'implicit_hate': self.config.implicit_hate_weight
        }
        
        # 计算加权平均
        weighted_sum = 0.0
        total_weight = 0.0
        
        for score_type, score_value in scores.items():
            weight_key = f"{score_type}_weight"
            weight = weights.get(weight_key, default_weights.get(score_type, 1.0))
            
            weighted_sum += score_value * weight
            total_weight += weight
        
        # 归一化
        ambiguity_score = weighted_sum / total_weight if total_weight > 0 else 0.0
        
        return min(ambiguity_score, 1.0)
    
    def get_ambiguity_explanation(self, 
                                 text: str,
                                 model_results: Dict[str, Any],
                                 dataset_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取详细的歧义解释
        
        Returns:
            包含歧义分析详情的字典
        """
        is_ambiguous, reasons, score = self.detect_ambiguity(text, model_results, dataset_name)
        
        # 提取特征
        features = self.feature_extractor.extract_features(text, model_results)
        feature_importance = self.feature_extractor.get_feature_importance_scores(features)
        
        # 构建解释
        explanation = {
            'is_ambiguous': is_ambiguous,
            'ambiguity_score': score,
            'reasons': reasons,
            'features': features,
            'feature_importance': feature_importance,
            'model_results_summary': self._summarize_model_results(model_results),
            'recommendation': self._get_recommendation(score, reasons)
        }
        
        return explanation
    
    def _summarize_model_results(self, model_results: Dict[str, Any]) -> Dict[str, Any]:
        """总结模型结果"""
        verdicts = []
        confidences = []
        
        for model_name, result in model_results.items():
            if isinstance(result, dict):
                verdicts.append(result.get('verdict', 0))
                confidences.append(result.get('confidence', 0.5))
        
        return {
            'verdict_distribution': {
                'hate': sum(verdicts),
                'non_hate': len(verdicts) - sum(verdicts)
            },
            'confidence_stats': {
                'mean': np.mean(confidences) if confidences else 0,
                'std': np.std(confidences) if confidences else 0,
                'min': np.min(confidences) if confidences else 0,
                'max': np.max(confidences) if confidences else 0
            }
        }
    
    def _get_recommendation(self, score: float, reasons: List[str]) -> str:
        """获取处理建议"""
        if score >= 0.8:
            return "强烈建议使用第二层LLM进行深度分析"
        elif score >= 0.5:
            return "建议使用第二层LLM进行进一步分析"
        elif score >= 0.3:
            return "可以考虑使用第二层LLM，但第一层结果相对可靠"
        else:
            return "第一层结果较为可靠，无需第二层分析"
