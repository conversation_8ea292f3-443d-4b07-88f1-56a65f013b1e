#!/usr/bin/env python3
"""
增强歧义检测器
Enhanced Ambiguity Detector

基于多种策略和特征的智能歧义检测系统
包括置信度分析、模型分歧检测、特征异常检测等

作者: AI Assistant
日期: 2025-08-02
版本: 1.0
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import logging

from .text_feature_extractor import TextFeatureExtractor, FeatureConfig

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AmbiguityConfig:
    """歧义检测配置"""
    # 置信度阈值策略 - 基于日志分析优化
    confidence_low_threshold: float = 0.25   # 降低低置信度阈值
    confidence_high_threshold: float = 0.75  # 提高高置信度阈值
    confidence_variance_threshold: float = 0.25  # 提高方差阈值，减少触发

    # 模型分歧策略
    enable_disagreement_detection: bool = True
    disagreement_weight: float = 0.8  # 降低分歧权重，减少第二层使用

    # 综合评分阈值 - 提高阈值减少第二层使用
    ambiguity_threshold: float = 0.6
    


class EnhancedAmbiguityDetector:
    """增强歧义检测器"""
    
    def __init__(self, config: AmbiguityConfig = None):
        """
        初始化增强歧义检测器
        
        Args:
            config: 歧义检测配置
        """
        self.config = config or AmbiguityConfig()
        
        # 初始化特征提取器
        feature_config = FeatureConfig()
        self.feature_extractor = TextFeatureExtractor(feature_config)
        

        
        logger.info("增强歧义检测器初始化完成")
    

    
    def detect_ambiguity(self, 
                        text: str,
                        model_results: Dict[str, Any],
                        dataset_name: Optional[str] = None) -> Tuple[bool, List[str], float]:
        """
        检测文本是否存在歧义
        
        Args:
            text: 输入文本
            model_results: 模型预测结果字典
            dataset_name: 数据集名称（用于特定权重）
            
        Returns:
            (is_ambiguous, reasons, ambiguity_score)
        """
        reasons = []
        scores = {}
        
        # 提取文本特征
        features = self.feature_extractor.extract_features(text, model_results)
        
        # 策略1：置信度阈值检测
        confidence_score, confidence_reasons = self._detect_confidence_ambiguity(model_results)
        if confidence_score > 0:
            scores['confidence'] = confidence_score
            reasons.extend(confidence_reasons)
        
        # 策略2：模型分歧检测
        if self.config.enable_disagreement_detection:
            disagreement_score, disagreement_reasons = self._detect_disagreement_ambiguity(model_results)
            if disagreement_score > 0:
                scores['disagreement'] = disagreement_score
                reasons.extend(disagreement_reasons)
        

        

        
        # 计算综合歧义分数
        ambiguity_score = self._calculate_ambiguity_score(scores, dataset_name)
        
        # 判断是否存在歧义
        is_ambiguous = ambiguity_score >= self.config.ambiguity_threshold
        
        return is_ambiguous, reasons, ambiguity_score
    
    def _detect_confidence_ambiguity(self, model_results: Dict[str, Any]) -> Tuple[float, List[str]]:
        """检测基于置信度的歧义"""
        reasons = []
        score = 0.0
        
        confidences = []
        for model_name, result in model_results.items():
            if isinstance(result, dict) and 'confidence' in result:
                confidence = result['confidence']
                confidences.append(confidence)
                
                # 检查置信度是否在歧义范围内
                if self.config.confidence_low_threshold <= confidence <= self.config.confidence_high_threshold:
                    reasons.append(f"{model_name}置信度在歧义范围内: {confidence:.3f}")
                    score += 0.5
        
        # 检查置信度方差
        if len(confidences) > 1:
            confidence_variance = np.var(confidences)
            if confidence_variance > self.config.confidence_variance_threshold:
                reasons.append(f"模型置信度方差过大: {confidence_variance:.3f}")
                score += 0.3
        
        # 检查极低置信度
        low_confidence_count = sum(1 for conf in confidences if conf < self.config.confidence_low_threshold)
        if low_confidence_count > 0:
            reasons.append(f"{low_confidence_count}个模型置信度过低")
            score += 0.4
        
        return min(score, 1.0), reasons
    
    def _detect_disagreement_ambiguity(self, model_results: Dict[str, Any]) -> Tuple[float, List[str]]:
        """检测基于模型分歧的歧义"""
        reasons = []
        score = 0.0
        
        verdicts = []
        for model_name, result in model_results.items():
            if isinstance(result, dict) and 'verdict' in result:
                verdicts.append((model_name, result['verdict']))
        
        if len(verdicts) < 2:
            return 0.0, reasons
        
        # 检查预测分歧
        unique_verdicts = set(verdict for _, verdict in verdicts)
        if len(unique_verdicts) > 1:
            hate_count = sum(1 for _, verdict in verdicts if verdict == 1)
            non_hate_count = len(verdicts) - hate_count
            
            reasons.append(f"模型预测分歧: {hate_count}个认为是仇恨言论, {non_hate_count}个认为不是")
            
            # 分歧程度评分
            if hate_count == non_hate_count:
                score = 1.0  # 完全分歧
            else:
                minority_ratio = min(hate_count, non_hate_count) / len(verdicts)
                score = minority_ratio * 2  # 少数派比例越高，分歧越严重
        
        return score, reasons
    

    

    
    def _calculate_ambiguity_score(self, scores: Dict[str, float], dataset_name: Optional[str] = None) -> float:
        """计算综合歧义分数"""
        if not scores:
            return 0.0

        # 统一权重配置
        default_weights = {
            'confidence': 1.0,
            'disagreement': self.config.disagreement_weight
        }

        # 计算加权平均
        weighted_sum = 0.0
        total_weight = 0.0

        for score_type, score_value in scores.items():
            weight = default_weights.get(score_type, 1.0)

            weighted_sum += score_value * weight
            total_weight += weight

        # 归一化
        ambiguity_score = weighted_sum / total_weight if total_weight > 0 else 0.0

        return min(ambiguity_score, 1.0)
    
    def get_ambiguity_explanation(self, 
                                 text: str,
                                 model_results: Dict[str, Any],
                                 dataset_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取详细的歧义解释
        
        Returns:
            包含歧义分析详情的字典
        """
        is_ambiguous, reasons, score = self.detect_ambiguity(text, model_results, dataset_name)
        
        # 提取特征
        features = self.feature_extractor.extract_features(text, model_results)
        feature_importance = self.feature_extractor.get_feature_importance_scores(features)
        
        # 构建解释
        explanation = {
            'is_ambiguous': is_ambiguous,
            'ambiguity_score': score,
            'reasons': reasons,
            'features': features,
            'feature_importance': feature_importance,
            'model_results_summary': self._summarize_model_results(model_results),
            'recommendation': self._get_recommendation(score, reasons)
        }
        
        return explanation
    
    def _summarize_model_results(self, model_results: Dict[str, Any]) -> Dict[str, Any]:
        """总结模型结果"""
        verdicts = []
        confidences = []
        
        for model_name, result in model_results.items():
            if isinstance(result, dict):
                verdicts.append(result.get('verdict', 0))
                confidences.append(result.get('confidence', 0.5))
        
        return {
            'verdict_distribution': {
                'hate': sum(verdicts),
                'non_hate': len(verdicts) - sum(verdicts)
            },
            'confidence_stats': {
                'mean': np.mean(confidences) if confidences else 0,
                'std': np.std(confidences) if confidences else 0,
                'min': np.min(confidences) if confidences else 0,
                'max': np.max(confidences) if confidences else 0
            }
        }
    
    def _get_recommendation(self, score: float, reasons: List[str]) -> str:
        """获取处理建议"""
        if score >= 0.8:
            return "强烈建议使用第二层LLM进行深度分析"
        elif score >= 0.5:
            return "建议使用第二层LLM进行进一步分析"
        elif score >= 0.3:
            return "可以考虑使用第二层LLM，但第一层结果相对可靠"
        else:
            return "第一层结果较为可靠，无需第二层分析"
