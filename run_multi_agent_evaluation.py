import argparse
import os
import sys
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core_modules.systems import TwoLayerDetectionSystem, TwoLayerConfig
from offensive_speech_detection.evaluator import ModelEvaluator
from offensive_speech_detection.data_loader import get_dataset_loader

def main():
    """Run two-layer multi-agent evaluation"""
    parser = argparse.ArgumentParser(description="Run two-layer multi-agent offensive speech detection evaluation")
    parser.add_argument("--dataset", type=str, default="ImplicitHate",
                        choices=["ImplicitHate", "HateSpeechOffensive", "HateSpeechStormfront"],
                        help="Name of the dataset to evaluate")
    parser.add_argument("--num_samples", type=int, default=10,
                        help="Number of samples to evaluate")
    parser.add_argument("--start_idx", type=int, default=0,
                        help="Starting index of samples")
    parser.add_argument("--model", type=str, default="gpt-3.5-turbo-0125",
                        help="Name of the LLM model to use for second layer")
    parser.add_argument("--provider", type=str, choices=["api", "ollama", "local"], default="api",
                        help="Model provider for LLM")
    parser.add_argument("--ollama-base-url", type=str, default="http://localhost:11434",
                        help="Ollama API base URL")
    parser.add_argument("--local-model-path", type=str,
                        help="Path to local model (used when provider is 'local')")

    # 两层系统配置参数
    parser.add_argument("--config-type", type=str, default="balanced",
                        choices=["conservative", "balanced", "aggressive"],
                        help="Configuration type for two-layer system")
    parser.add_argument("--enable-performance-optimization", action="store_true", default=True,
                        help="Enable performance optimization")
    parser.add_argument("--enable-caching", action="store_true", default=True,
                        help="Enable caching")
    parser.add_argument("--enable-parallel-processing", action="store_true", default=True,
                        help="Enable parallel processing")

    # 动态权重配置参数 (快速适应型默认值)
    parser.add_argument("--enable-dynamic-weights", action="store_true", default=True,
                        help="Enable dynamic weight learning for layer1 agents")
    parser.add_argument("--weight-learning-rate", type=float, default=0.02,
                        help="Learning rate for dynamic weight adjustment (快速适应型: 0.02)")
    parser.add_argument("--weight-min-samples", type=int, default=3,
                        help="Minimum samples before starting weight updates (快速适应型: 3)")
    parser.add_argument("--weight-performance-window", type=int, default=100,
                        help="Window size for performance tracking (快速适应型: 100)")
    parser.add_argument("--weight-min-weight", type=float, default=0.05,
                        help="Minimum weight value for any agent")
    parser.add_argument("--weight-max-weight", type=float, default=0.95,
                        help="Maximum weight value for any agent")
    parser.add_argument("--weight-enable-adaptive-lr", action="store_true", default=True,
                        help="Enable adaptive learning rate")
    parser.add_argument("--weight-log-updates", action="store_true", default=True,
                        help="Enable logging of weight updates")
    args = parser.parse_args()

    if args.provider == "local" and not args.local_model_path:
        parser.error("--local-model-path is required when using a local provider.")

    # 当使用本地模型时，自动从路径解析模型名称
    if args.provider == "local":
        args.model = os.path.basename(args.local_model_path.rstrip('/\\'))

    print(f"Starting two-layer multi-agent evaluation on {args.dataset} dataset")
    print(f"Number of samples: {args.num_samples}, starting index: {args.start_idx}")
    print(f"LLM provider: {args.provider}, model: {args.model}")
    print(f"Configuration type: {args.config_type}")
    print(f"Dynamic weights enabled: {args.enable_dynamic_weights}")
    if args.enable_dynamic_weights:
        print(f"  - Learning rate: {args.weight_learning_rate}")
        print(f"  - Min samples: {args.weight_min_samples}")
        print(f"  - Performance window: {args.weight_performance_window}")
        print(f"  - Weight range: [{args.weight_min_weight}, {args.weight_max_weight}]")
        print(f"  - Adaptive LR: {args.weight_enable_adaptive_lr}")
        print(f"  - Log updates: {args.weight_log_updates}")

    # 设置模型路径
    model_path = args.model
    if args.provider == "local":
        model_path = args.local_model_path
        print(f"Using local model at: {model_path}")

    # 创建两层系统配置
    if args.config_type == "conservative":
        config = TwoLayerConfig(
            layer1_confidence_threshold_high=0.8,
            layer1_confidence_threshold_low=0.3,
            layer1_consensus_threshold=0.7,
            layer2_llm_model=args.model,  # 使用提取的模型名称
            layer2_provider=args.provider,
            layer2_local_model_path=model_path if args.provider == "local" else None,  # 完整路径
            enable_performance_optimization=args.enable_performance_optimization,
            enable_caching=args.enable_caching,
            enable_parallel_processing=args.enable_parallel_processing,
            # 动态权重配置
            enable_dynamic_weights=args.enable_dynamic_weights,
            weight_learning_rate=args.weight_learning_rate,
            weight_min_samples=args.weight_min_samples,
            weight_performance_window=args.weight_performance_window,
            weight_min_weight=args.weight_min_weight,
            weight_max_weight=args.weight_max_weight,
            weight_enable_adaptive_lr=args.weight_enable_adaptive_lr,
            weight_log_updates=args.weight_log_updates
        )
    elif args.config_type == "aggressive":
        config = TwoLayerConfig(
            layer1_confidence_threshold_high=0.6,
            layer1_confidence_threshold_low=0.5,
            layer1_consensus_threshold=0.5,
            layer2_llm_model=args.model,  # 使用提取的模型名称
            layer2_provider=args.provider,
            layer2_local_model_path=model_path if args.provider == "local" else None,  # 完整路径
            enable_performance_optimization=args.enable_performance_optimization,
            enable_caching=args.enable_caching,
            enable_parallel_processing=args.enable_parallel_processing,
            # 动态权重配置
            enable_dynamic_weights=args.enable_dynamic_weights,
            weight_learning_rate=args.weight_learning_rate,
            weight_min_samples=args.weight_min_samples,
            weight_performance_window=args.weight_performance_window,
            weight_min_weight=args.weight_min_weight,
            weight_max_weight=args.weight_max_weight,
            weight_enable_adaptive_lr=args.weight_enable_adaptive_lr,
            weight_log_updates=args.weight_log_updates
        )
    else:  # balanced
        config = TwoLayerConfig(
            layer1_confidence_threshold_high=0.7,
            layer1_confidence_threshold_low=0.4,
            layer1_consensus_threshold=0.6,
            layer2_llm_model=args.model,  # 使用提取的模型名称
            layer2_provider=args.provider,
            layer2_local_model_path=model_path if args.provider == "local" else None,  # 完整路径
            enable_performance_optimization=args.enable_performance_optimization,
            enable_caching=args.enable_caching,
            enable_parallel_processing=args.enable_parallel_processing,
            # 动态权重配置
            enable_dynamic_weights=args.enable_dynamic_weights,
            weight_learning_rate=args.weight_learning_rate,
            weight_min_samples=args.weight_min_samples,
            weight_performance_window=args.weight_performance_window,
            weight_min_weight=args.weight_min_weight,
            weight_max_weight=args.weight_max_weight,
            weight_enable_adaptive_lr=args.weight_enable_adaptive_lr,
            weight_log_updates=args.weight_log_updates
        )

    # 创建两层检测系统
    detector = TwoLayerDetectionSystem(config)
    print(f"Two-layer system configuration:")
    print(f"  - Layer1 high threshold: {config.layer1_confidence_threshold_high}")
    print(f"  - Layer1 low threshold: {config.layer1_confidence_threshold_low}")
    print(f"  - Consensus threshold: {config.layer1_consensus_threshold}")
    print(f"  - Performance optimization: {config.enable_performance_optimization}")
    print(f"  - Caching enabled: {config.enable_caching}")
    print(f"  - Parallel processing: {config.enable_parallel_processing}")
    print(f"  - Dynamic weights: {config.enable_dynamic_weights}")
    if config.enable_dynamic_weights:
        print(f"    * Learning rate: {config.weight_learning_rate}")
        print(f"    * Min samples: {config.weight_min_samples}")
        print(f"    * Performance window: {config.weight_performance_window}")
        print(f"    * Weight range: [{config.weight_min_weight}, {config.weight_max_weight}]")
        print(f"    * Adaptive LR: {config.weight_enable_adaptive_lr}")
        print(f"    * Log updates: {config.weight_log_updates}")

    # 创建评估器
    evaluator = ModelEvaluator(
        detector=detector,
        dataset_name=args.dataset,
        num_samples=args.num_samples,
        start_idx=args.start_idx
    )

    # 运行评估
    evaluator.evaluate()

    # 获取系统统计信息
    stats = detector.get_statistics()
    print(f"\nTwo-layer system statistics:")
    print(f"  - Total processed: {stats.get('total_processed', 0)}")
    print(f"  - Layer1 only rate: {stats.get('layer1_only_rate', 0):.3f}")
    print(f"  - Layer2 trigger rate: {stats.get('layer2_trigger_rate', 0):.3f}")
    print(f"  - Average processing time: {stats.get('avg_total_time', 0):.3f}s")

    # 如果启用了动态权重，显示权重学习统计
    if config.enable_dynamic_weights and detector.weight_learner:
        weight_stats = detector.weight_learner.get_performance_stats()
        current_weights = detector.weight_learner.get_current_weights()
        print(f"\nDynamic weight learning statistics:")
        print(f"  - Current weights: ToxiGen={current_weights[0]:.3f}, HurtLex={current_weights[1]:.3f}")
        print(f"  - Total updates: {weight_stats.get('update_count', 0)}")
        print(f"  - Current learning rate: {weight_stats.get('current_learning_rate', 0):.4f}")
        print(f"  - ToxiGen accuracy: {weight_stats.get('toxigen_overall_accuracy', 0):.3f}")
        print(f"  - HurtLex accuracy: {weight_stats.get('hurtlex_overall_accuracy', 0):.3f}")

    # 清理资源
    detector.cleanup()


if __name__ == "__main__":
    main() 